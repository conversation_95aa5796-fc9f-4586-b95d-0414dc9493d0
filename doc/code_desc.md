# 物联网规则引擎代码结构说明

## 项目概述

- **项目名称**：IoT Rule Engine (物联网规则引擎)
- **技术栈**：Java 11 + Spring Boot 2.7.18 + Easy Rules 4.1.0 + SQLite 3.44.1.0 + MyBatis 2.3.2
- **架构模式**：分层架构 + 事件驱动 + 异步处理

## 包结构总览

```
com.inxaiot.ruleengine/
├── RuleEngineApplication.java          # Spring Boot主启动类
├── RuleEngineConfig.java               # 全局配置类
├── api/                                # API接口层
│   ├── controller/                     # REST控制器
│   └── manager/                        # 业务管理器
├── common/                             # 公共模块
│   ├── context/                        # 系统上下文
│   └── operator/                       # 操作符定义
├── core/                               # 规则引擎核心
│   ├── action/                         # 动作执行器
│   ├── adapter/                        # 规则适配器
│   ├── analyzer/                       # 依赖分析器
│   ├── definition/                     # 规则定义模型
│   └── engine/                         # 规则引擎服务
├── device/                             # 设备管理
│   ├── event/                          # 设备事件
│   └── state/                          # 设备状态管理
├── storage/                            # 数据持久化层
│   ├── entity/                         # 数据库实体
│   └── mapper/                         # MyBatis映射器
├── transport/                          # 数据传输层
│   └── mqtt/                           # MQTT消息处理
└── trigger/                            # 触发器模块
    └── time/                           # 时间触发器
```

## 核心类功能说明

### 应用启动和配置

#### RuleEngineApplication.java

- **功能**：Spring Boot主启动类
- **职责**：配置组件扫描、MyBatis映射扫描、启用定时任务
- **关键注解**：`@SpringBootApplication`, `@ComponentScan`, `@MapperScan`, `@EnableScheduling`

#### RuleEngineConfig.java

- **功能**：全局配置类，定义线程池和核心组件
- **核心配置**：
  - `ruleEvaluationExecutor`：规则评估线程池（CPU核心数线程）
  - `actionExecutionExecutor`：动作执行线程池（CPU核心数*2线程）
  - `deviceStateScheduler`：设备状态管理定时器（2线程）
  - `timeTriggerScheduler`：时间触发器定时器（1线程）

### API接口层

#### api.controller包

- **RuleController.java**：规则管理API，提供规则的CRUD操作
- **MonitorController.java**：监控API，提供系统状态和统计信息
- **ConfigController.java**：配置管理API，提供全局配置的查询和更新

#### api.manager包

- **RuleManager.java**：规则业务管理器，封装复杂的规则操作逻辑

### 规则引擎核心模块

#### core.definition包 - 数据模型

- **RuleDefinition.java**：规则定义核心模型
  - 字段：ruleId, ruleName, targetDeviceId, bizId, priority, enabled
  - 支持：事件驱动/时间驱动两种触发类型
  - 包含：时间条件、触发条件、动作定义
- **TimeCondition.java**：时间条件定义
  - 支持：Cron表达式、工作日、季节、包含/排除日期
  - 逻辑：AND/OR组合逻辑
- **TriggerCondition.java**：设备触发条件组合
  - 逻辑：ALL(AND)/ANY(OR)匹配模式
  - 包含：多个DeviceCondition的组合
- **DeviceCondition.java**：单个设备条件
  - 字段：sourceDeviceId, pointId, operator, value, durationMinutes
  - 支持：基础比较、范围判断、持续时间条件
- **ActionDefinition.java**：动作定义
  - 类型：设备控制、消息发送、API调用、日志记录
  - 参数：目标设备、动作参数、类型参数

#### core.engine包 - 规则引擎服务

- **RuleEngineService.java**：规则引擎服务接口
- **RuleEngineServiceImpl.java**：规则引擎服务实现
  - **核心方法**：
    - `processDeviceEvent()`：处理设备事件
    - `handleStateChangeEvent()`：处理状态变化事件
    - `triggerRuleActivation()`：触发规则激活
  - **异步处理**：使用`@Async`注解实现异步规则评估
  - **事件驱动**：使用`@EventListener`监听状态变化事件

#### core.adapter包 - 规则适配器

- **RuleAdapterService.java**：规则适配器服务
  - **核心功能**：将RuleDefinition转换为Easy Rules的Rule对象
  - **条件评估**：集成时间条件和设备条件的综合判断
  - **动作执行**：规则触发时调用ActionExecutor执行动作

#### core.analyzer包 - 分析器

- **DependencyAnalyzer.java**：依赖分析器
  - **功能**：分析规则依赖的设备点位
  - **优化**：支持批量状态查询，提高性能
- **FactsBuilder.java**：Facts构建器
  - **功能**：为规则评估构建完整的Facts对象
  - **聚合**：收集规则需要的所有设备状态数据
  - **缓存**：支持预加载状态数据的批量优化

#### core.action包 - 动作执行器

- **ActionExecutor.java**：动作执行器
  - **异步执行**：所有动作异步执行，不阻塞规则评估
  - **去重机制**：30分钟窗口内的重复操作检测
  - **支持类型**：设备控制、消息发送、API调用、日志记录
  - **错误处理**：完善的异常处理和重试机制

### 设备管理模块

#### device.state包 - 设备状态管理

- **StateManager.java**：设备状态管理器
  - **状态缓存**：使用ConcurrentHashMap缓存设备点位状态
  - **持续监控**：支持"无人X分钟"等持续时间条件
  - **跨天处理**：自动检测日期变化并清理状态
  - **事件发布**：通过DeviceEventPublisher发布状态变化事件
- **DevicePointState.java**：设备点位状态模型
  - **状态信息**：当前值、前一个值、更新时间、持续时间
  - **数据类型**：支持多种数据类型的状态管理
- **StateCondition.java**：状态条件定义
- **DevicePointRef.java**：设备点位引用

#### device.event包 - 设备事件

- **DeviceEventPublisher.java**：设备事件发布器
  - **解耦设计**：使用Spring事件机制解耦StateManager和RuleEngineService
- **StateChangeEvent.java**：状态变化事件模型

### 时间触发器模块

#### trigger.time包

- **TimeConditionEvaluator.java**：时间条件评估器
  - **复杂时间逻辑**：支持多层时间条件的组合评估
  - **优先级处理**：包含日期 > 排除日期 > 季节 > 工作日 > Cron表达式
  - **缓存机制**：全局日历数据缓存和自动刷新
- **GlobalCalendar.java**：全局日历模型
  - **节假日管理**：支持节假日列表
  - **季节定义**：支持夏季/冬季时间段定义
- **TimeRange.java**：时间范围模型
- **TimeSchedulerService.java**：时间触发服务（时间触发器）

### 数据传输层

#### transport.mqtt包

- **MqttListener.java**：MQTT消息监听器
  - **消息解析**：解析设备上报的MQTT消息
  - **状态更新**：调用StateManager更新设备状态
- **DeviceDataMessage.java**：设备数据消息模型

### 存储层

#### storage包

- **RuleService.java**：规则数据访问服务
  - **CRUD操作**：规则的增删改查
  - **条件查询**：支持多种条件的规则查询
  - **缓存管理**：规则数据的缓存和刷新
- **GlobalCalendarService.java**：全局日历服务

#### storage.entity包

- **RuleDefinitionEntity.java**：规则定义数据库实体
- **GlobalCalendarEntity.java**：全局日历数据库实体

#### storage.mapper包

- **RuleDefinitionMapper.java**：规则定义MyBatis映射器
- **GlobalCalendarMapper.java**：全局日历MyBatis映射器

### 公共模块

#### common.context包

- **SystemContextService.java**：系统上下文服务接口
- **SystemContextServiceImpl.java**：系统上下文服务实现
  - **全局信息**：引擎ID、区域ID、时区、版本等
  - **动态上下文**：当前时间、调试模式等

#### common.operator包

- **Operators.java**：操作符常量定义
  - **基础操作符**：EQUALS, NOT_EQUALS, GREATER_THAN, LESS_THAN等
  - **字符串操作符**：CONTAINS, STARTS_WITH, ENDS_WITH等
  - **范围操作符**：BETWEEN, NOT_BETWEEN等
  - **持续时间操作符**：STATES_KEEP_MINUTES等
- **ValueComparator.java**：值比较器
  - **类型转换**：支持字符串、数值、布尔类型的比较
  - **范围比较**：支持BETWEEN操作的上下限比较

#### common包

- **WorkDayUtils.java**：工作日工具类

## 关键接口和抽象类

### 服务接口

- **RuleEngineService**：规则引擎服务接口，定义核心业务方法
- **SystemContextService**：系统上下文服务接口

### 数据模型接口

所有数据模型类都是POJO类，使用Jackson注解支持JSON序列化/反序列化。

## 配置文件和常量定义

### application.yml

- **数据库配置**：SQLite连接参数和连接池配置
- **日志配置**：日志级别、输出格式、文件轮转
- **线程池配置**：各个线程池的核心参数
- **业务配置**：规则引擎的业务参数

### 常量定义

- **Operators类**：集中定义所有操作符常量
- **ActionDefinition.ActionTypes**：动作类型常量
- **RuleDefinition.TriggerType**：触发类型枚举

## 数据模型和实体类

### 核心数据模型层次结构

```mermaid
classDiagram
    class RuleDefinition {
        +String ruleId
        +String ruleName
        +String targetDeviceId
        +TriggerType triggerType
        +List~TimeCondition~ timeConditions
        +TriggerCondition triggerCondition
        +List~ActionDefinition~ actions
    }

    class TimeCondition {
        +List~String~ timeCronExpressions
        +List~String~ workDays
        +String season
        +List~LocalDate~ includeDates
        +List~LocalDate~ excludeDates
        +TimeLogic logic
    }

    class TriggerCondition {
        +MatchLogic logic
        +List~DeviceCondition~ conditions
    }

    class DeviceCondition {
        +String sourceDeviceId
        +String pointId
        +String operator
        +Object value
        +long durationMinutes
    }

    class ActionDefinition {
        +String actionType
        +String targetDeviceId
        +Map~String,Object~ params
        +Map~String,Object~ typeParams
    }

    RuleDefinition --> TimeCondition
    RuleDefinition --> TriggerCondition
    RuleDefinition --> ActionDefinition
    TriggerCondition --> DeviceCondition
```

## 技术特性总结

### 1. 异步处理架构

- **分层异步**：规则评估和动作执行使用独立线程池
- **事件驱动**：使用Spring事件机制实现组件解耦
- **资源隔离**：CPU密集型和IO密集型任务分离

### 2. 状态管理机制

- **内存缓存**：设备状态使用ConcurrentHashMap缓存
- **持续监控**：支持复杂的持续时间条件判断
- **跨天处理**：自动处理日期变化和状态清理

### 3. 时间条件支持

- **多层评估**：支持包含/排除日期、季节、工作日、Cron表达式的组合
- **优先级机制**：不同时间条件按优先级顺序评估
- **缓存优化**：全局日历数据缓存和按需刷新

### 4. 扩展性设计

- **操作符扩展**：集中定义操作符，易于扩展新的比较逻辑
- **动作类型扩展**：支持新的动作类型插件化扩展
- **数据源扩展**：支持多种数据源的适配器模式

### 5. 错误处理和监控

- **异常隔离**：动作执行失败不影响规则引擎核心
- **重复操作检测**：30分钟窗口的重复操作去重
- **完善日志**：分级日志记录，支持问题排查和性能监控

## 核心实现细节

### 1. 规则执行流程详解

#### 事件驱动规则执行流程

```mermaid
sequenceDiagram
    participant MQTT as MQTT消息
    participant ML as MqttListener
    participant SM as StateManager
    participant DEP as DeviceEventPublisher
    participant RES as RuleEngineService
    participant FB as FactsBuilder
    participant DA as DependencyAnalyzer
    participant RAS as RuleAdapterService
    participant TCE as TimeConditionEvaluator
    participant AE as ActionExecutor

    MQTT->>ML: 设备数据消息
    ML->>SM: processDevicePointUpdate()
    SM->>SM: 更新设备状态缓存
    SM->>SM: 检查状态条件监控
    SM->>DEP: publishDeviceStateChange()
    DEP->>RES: handleStateChangeEvent() [异步]
    RES->>RES: findRulesRelatedToDevice()

    loop 每个相关规则
        RES->>DA: extractRequiredDevicePoints()
        RES->>FB: buildCompleteFactsForRule()
        FB->>SM: 批量获取设备状态
        RES->>RAS: adapt(ruleDefinition)
        RAS->>TCE: isTimeConditionMet()
        RAS->>RAS: evaluateDeviceTriggerConditions()

        alt 条件满足
            RAS->>AE: executeAction() [异步]
            AE->>AE: 检查重复操作
            AE->>AE: 执行具体动作
        end
    end
```

#### 时间驱动规则执行流程

```mermaid
sequenceDiagram
    participant TSS as TimeSchedulerService
    participant RES as RuleEngineService
    participant RAS as RuleAdapterService
    participant AE as ActionExecutor

    TSS->>TSS: 定时检查(每分钟)
    TSS->>TSS: 查找TIME_DRIVEN规则

    loop 每个时间驱动规则
        TSS->>TSS: 检查时间条件
        alt 时间条件满足且状态变化
            TSS->>RES: triggerRuleActivation()
            RES->>RAS: adapt(ruleDefinition)
            RAS->>AE: executeAction()
        end
    end
```

### 2. 状态管理机制详解

#### 设备状态生命周期

```mermaid
stateDiagram-v2
    [*] --> 初始化: 首次接收数据
    初始化 --> 活跃: 状态正常更新
    活跃 --> 过期: 超过TTL时间
    过期 --> 活跃: 接收新数据
    活跃 --> 监控中: 满足持续条件
    监控中 --> 活跃: 条件不再满足
    监控中 --> 触发: 持续时间达到
    触发 --> 活跃: 重置状态
    过期 --> 清理: 系统清理
    清理 --> [*]
```

#### 状态条件监控实现

```java
public class StateConditionMonitor {
    private final StateCondition condition;
    private final ScheduledFuture<?> timeoutTask;
    private volatile boolean enabled = true;

    public void checkCondition(DevicePointState currentState) {
        boolean conditionMet = evaluateCondition(currentState);

        if (conditionMet && !hasActiveTimeoutTask()) {
            // 条件满足，启动超时监控
            startTimeoutMonitoring();
        } else if (!conditionMet && hasActiveTimeoutTask()) {
            // 条件不满足，取消监控
            cancelTimeoutTask();
        }
    }

    private void startTimeoutMonitoring() {
        long delayMillis = condition.getDurationMinutes() * 60 * 1000;
        this.timeoutTask = scheduler.schedule(() -> {
            // 超时触发，发布持续条件满足事件
            publishConditionMetEvent();
        }, delayMillis, TimeUnit.MILLISECONDS);
    }
}
```

### 3. Facts构建优化策略

#### 批量状态获取优化

```java
@Component
public class FactsBuilder {

    public Facts buildCompleteFactsForRule(RuleDefinition rule, String triggerDeviceId,
                                         String triggerPointId, Object triggerValue) {
        Facts facts = new Facts();

        // 1. 添加触发信息
        addTriggerInfo(facts, triggerDeviceId, triggerPointId, triggerValue);

        // 2. 分析依赖并批量获取状态
        Set<DevicePointRef> dependencies = dependencyAnalyzer.extractRequiredDevicePoints(rule);
        Map<String, DevicePointState> batchStates = stateManager.getMultipleDeviceStates(dependencies);

        // 3. 构建完整Facts
        for (DevicePointRef ref : dependencies) {
            String stateKey = ref.getStateKey();
            DevicePointState state = batchStates.get(stateKey);

            if (state != null && !isStateExpired(state, ref.getMaxAge())) {
                addDeviceStateToFacts(facts, ref, state);
            } else {
                facts.put(stateKey + "_missing", true);
            }
        }

        return facts;
    }
}
```

### 4. 动作执行去重机制

#### 重复操作检测实现

```java
@Service
public class ActionExecutor {
    // 操作记录缓存：Key = ruleId + deviceId + pointId, Value = 最后执行时间
    private final Map<String, Long> operationHistory = new ConcurrentHashMap<>();

    private boolean isDuplicateOperation(String ruleId, String targetDeviceId,
                                       String pointId, ActionDefinition actionDef) {
        String operationKey = generateOperationKey(ruleId, targetDeviceId, pointId, actionDef);
        Long lastExecutionTime = operationHistory.get(operationKey);

        if (lastExecutionTime == null) {
            return false; // 首次执行
        }

        long currentTime = System.currentTimeMillis();
        long timeDiff = currentTime - lastExecutionTime;
        long windowMillis = DUPLICATE_PREVENTION_WINDOW_MINUTES * 60 * 1000;

        return timeDiff < windowMillis;
    }

    private void recordOperation(String ruleId, String targetDeviceId,
                               String pointId, ActionDefinition actionDef) {
        String operationKey = generateOperationKey(ruleId, targetDeviceId, pointId, actionDef);
        operationHistory.put(operationKey, System.currentTimeMillis());

        // 定期清理过期记录
        scheduleCleanupIfNeeded();
    }
}
```

### 5. 时间条件评估优化

#### 多层时间条件评估

```java
@Service
public class TimeConditionEvaluator {

    private boolean evaluateSingleTimeCondition(TimeCondition condition, LocalDateTime evaluationTime) {
        LocalDate evaluationDate = evaluationTime.toLocalDate();

        // 优先级1：强制包含日期
        if (isInIncludeDates(condition, evaluationDate)) {
            return checkCronExpressions(condition.getTimeCronExpressions(), evaluationTime);
        }

        // 优先级2：强制排除日期
        if (isInExcludeDates(condition, evaluationDate)) {
            return false;
        }

        // 优先级3：季节条件
        if (!isInSeason(condition, evaluationDate)) {
            return false;
        }

        // 优先级4：工作日条件
        if (!isWorkDay(condition, evaluationDate)) {
            return false;
        }

        // 优先级5：时间段条件
        return checkCronExpressions(condition.getTimeCronExpressions(), evaluationTime);
    }
}
```

## 性能优化实现

### 1. 内存管理优化

- **状态缓存TTL**：设备状态自动过期清理
- **规则缓存**：热点规则内存缓存，减少数据库查询
- **批量操作**：多设备状态批量获取，减少Map查找次数

### 2. 并发优化

- **ConcurrentHashMap**：线程安全的状态缓存
- **读写分离**：读操作不加锁，写操作使用细粒度锁
- **异步处理**：规则评估和动作执行完全异步化

### 3. 数据库优化

- **索引设计**：基于查询模式设计合适的索引
- **连接池**：合理配置数据库连接池参数
- **批量操作**：支持规则的批量插入和更新

## 扩展点设计

### 1. 操作符扩展机制

```java
public class Operators {
    // 基础操作符
    public static class Basic {
        public static final String EQUALS = "EQUALS";
        public static final String NOT_EQUALS = "NOT_EQUALS";
        // ... 其他基础操作符
    }

    // 持续时间操作符
    public static class Duration {
        public static final String STATES_KEEP_MINUTES = "STATES_KEEP_MINUTES";
        public static final String STATES_KEEP_SECONDS = "STATES_KEEP_SECONDS";
        // ... 其他持续时间操作符
    }

    // 扩展操作符（预留）
    public static class Extended {
        // 可以在这里添加新的操作符类型
    }
}
```

### 2. 动作类型扩展机制

```java
public class ActionDefinition {
    public static class ActionTypes {
        public static final String DEVICE_CONTROL = "DEVICE_CONTROL";
        public static final String SEND_MESSAGE = "SEND_MESSAGE";
        public static final String CALL_API = "CALL_API";
        public static final String LOG_EVENT = "LOG_EVENT";
        // 预留扩展点
        public static final String CUSTOM_ACTION = "CUSTOM_ACTION";
    }
}
```

## 问题和风险点

### 1. 已识别的技术风险

- **内存泄漏风险**：长时间运行可能导致状态缓存过大
- **时间同步问题**：跨天处理和时区变化的边界情况
- **并发安全问题**：高并发场景下的状态一致性

### 2. 代码质量问题

- **包结构重复**：新旧包结构并存，需要清理
- **测试覆盖不足**：单元测试覆盖率有待提高
- **文档不同步**：代码变更后文档更新不及时

### 3. 性能瓶颈点

- **数据库查询**：复杂条件查询可能成为瓶颈
- **状态缓存大小**：大量设备状态可能占用过多内存
- **线程池配置**：不合理的线程池配置影响性能
